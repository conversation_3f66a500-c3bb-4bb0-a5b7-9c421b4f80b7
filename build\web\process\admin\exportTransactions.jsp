<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>
<%@page import="java.io.*"%>
<%@page import="java.text.SimpleDateFormat"%>
<%@page import="java.util.Date"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Check if user is logged in as admin
    String adminId = (String) session.getAttribute("adminId");
    if (adminId == null) {
        response.sendRedirect("../../form/signin.jsp");
        return;
    }

    String format = request.getParameter("format");
    if (format == null || (!format.equals("excel") && !format.equals("pdf"))) {
        session.setAttribute("notification", "Format ekspor tidak valid!");
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=transaction");
        return;
    }

    try {
        // Query untuk mengambil semua data transaksi
        String query = "SELECT t.*, u.name as user_name, u.email as user_email, s.name as service_name " +
                     "FROM transaction t " +
                     "LEFT JOIN user u ON t.user_id = u.id " +
                     "LEFT JOIN services s ON t.service_id = s.id " +
                     "ORDER BY t.id ASC";
        PreparedStatement ps = conn.prepareStatement(query);
        ResultSet rs = ps.executeQuery();

        String exportTimestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());

        if (format.equals("excel")) {
            // Export to Excel (CSV format)
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment; filename=\"data_transaksi_" + exportTimestamp + ".csv\"");
            
            PrintWriter writer = response.getWriter();
            
            // Write CSV header
            writer.println("ID,Nama Layanan,Pengguna,Email Pengguna,Jumlah,Unit,Total,Status,Tanggal Dibuat");
            
            // Write data rows
            while (rs.next()) {
                int id = rs.getInt("id");
                String serviceName = rs.getString("service_name");
                String userName = rs.getString("user_name");
                String userEmail = rs.getString("user_email");
                double amount = rs.getDouble("amount");
                int unit = rs.getInt("unit");
                double total = rs.getDouble("total");
                String status = rs.getString("status");
                String createdAt = rs.getString("created_at");
                
                // Convert status to Indonesian
                String statusText = "";
                switch(status) {
                    case "menunggu":
                    case "pending":
                        statusText = "Menunggu";
                        break;
                    case "diterima":
                    case "completed":
                        statusText = "Diterima";
                        break;
                    case "ditolak":
                    case "cancelled":
                        statusText = "Ditolak";
                        break;
                    default:
                        statusText = status;
                }
                
                // Escape commas and quotes in CSV
                serviceName = serviceName != null ? "\"" + serviceName.replace("\"", "\"\"") + "\"" : "\"Layanan Tidak Ditemukan\"";
                userName = userName != null ? "\"" + userName.replace("\"", "\"\"") + "\"" : "\"User Tidak Ditemukan\"";
                userEmail = userEmail != null ? "\"" + userEmail.replace("\"", "\"\"") + "\"" : "\"Email Tidak Ditemukan\"";
                
                writer.println(id + "," + serviceName + "," + userName + "," + userEmail + "," + 
                             String.format("%.0f", amount) + "," + unit + "," + 
                             String.format("%.0f", total) + ",\"" + statusText + "\",\"" + createdAt + "\"");
            }
            
            writer.flush();
            writer.close();
            
        } else if (format.equals("pdf")) {
            // Export to PDF-ready HTML format
            response.setContentType("text/html; charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"laporan_transaksi_" + exportTimestamp + ".html\"");
            
            PrintWriter writer = response.getWriter();
            
            // Write HTML header with PDF-optimized styling
            writer.println("<!DOCTYPE html>");
            writer.println("<html>");
            writer.println("<head>");
            writer.println("<meta charset='UTF-8'>");
            writer.println("<meta name='viewport' content='width=device-width, initial-scale=1.0'>");
            writer.println("<title>Laporan Data Transaksi - Arqeta</title>");
            writer.println("<style>");
            writer.println("@media print { @page { size: A4; margin: 0.5in; } }");
            writer.println("* { margin: 0; padding: 0; box-sizing: border-box; }");
            writer.println("body { font-family: 'Arial', sans-serif; font-size: 11px; line-height: 1.3; color: #333; background: #fff; }");
            writer.println(".container { max-width: 100%; margin: 0 auto; padding: 20px; }");
            writer.println(".header { text-align: center; margin-bottom: 25px; border-bottom: 2px solid #007bff; padding-bottom: 15px; }");
            writer.println(".company-name { font-size: 16px; font-weight: bold; color: #2c3e50; margin-bottom: 5px; }");
            writer.println(".report-title { font-size: 18px; font-weight: bold; color: #007bff; margin-bottom: 10px; }");
            writer.println(".export-info { font-size: 10px; color: #666; }");
            writer.println(".summary { background: #f8f9fa; padding: 15px; margin-bottom: 20px; border-radius: 5px; }");
            writer.println(".summary-title { font-size: 12px; font-weight: bold; margin-bottom: 10px; color: #2c3e50; }");
            writer.println(".summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; }");
            writer.println(".summary-item { text-align: center; }");
            writer.println(".summary-label { font-size: 9px; color: #666; display: block; }");
            writer.println(".summary-value { font-size: 12px; font-weight: bold; color: #007bff; }");
            writer.println("table { width: 100%; border-collapse: collapse; margin-bottom: 20px; font-size: 10px; }");
            writer.println("th { background: #007bff; color: white; padding: 8px 4px; text-align: center; font-weight: bold; border: 1px solid #0056b3; }");
            writer.println("td { padding: 6px 4px; border: 1px solid #dee2e6; text-align: left; vertical-align: top; }");
            writer.println("tr:nth-child(even) { background: #f8f9fa; }");
            writer.println(".text-center { text-align: center; }");
            writer.println(".text-right { text-align: right; }");
            writer.println(".currency { font-weight: bold; color: #28a745; }");
            writer.println(".status-pending { background: #fff3cd; color: #856404; padding: 2px 6px; border-radius: 3px; font-size: 9px; }");
            writer.println(".status-approved { background: #d4edda; color: #155724; padding: 2px 6px; border-radius: 3px; font-size: 9px; }");
            writer.println(".status-rejected { background: #f8d7da; color: #721c24; padding: 2px 6px; border-radius: 3px; font-size: 9px; }");
            writer.println(".footer { text-align: center; margin-top: 30px; padding-top: 15px; border-top: 1px solid #dee2e6; font-size: 9px; color: #666; }");
            writer.println(".print-instruction { background: #e7f3ff; padding: 10px; margin: 10px 0; border-radius: 5px; text-align: center; font-size: 11px; }");
            writer.println("</style>");
            writer.println("<script>");
            writer.println("window.onload = function() {");
            writer.println("  setTimeout(function() { window.print(); }, 1000);");
            writer.println("};");
            writer.println("</script>");
            writer.println("</head>");
            writer.println("<body>");
            writer.println("<div class='container'>");
            
            // Header
            writer.println("<div class='header'>");
            writer.println("<div class='company-name'>PT. ARQETA DIGITAL SOLUTIONS</div>");
            writer.println("<div class='report-title'>LAPORAN DATA TRANSAKSI</div>");
            writer.println("<div class='export-info'>Diekspor pada: " + 
                new SimpleDateFormat("dd MMMM yyyy, HH:mm:ss").format(new Date()) + " WIB</div>");
            writer.println("</div>");
            
            // Calculate summary statistics
            rs.beforeFirst(); // Reset ResultSet
            int totalTransactions = 0;
            double totalAmount = 0;
            int pendingCount = 0;
            int approvedCount = 0;
            int rejectedCount = 0;
            
            while (rs.next()) {
                totalTransactions++;
                totalAmount += rs.getDouble("total");
                String status = rs.getString("status");
                switch(status) {
                    case "menunggu":
                    case "pending":
                        pendingCount++;
                        break;
                    case "diterima":
                    case "completed":
                        approvedCount++;
                        break;
                    case "ditolak":
                    case "cancelled":
                        rejectedCount++;
                        break;
                }
            }
            
            // Summary section
            writer.println("<div class='summary'>");
            writer.println("<div class='summary-title'>Ringkasan Transaksi</div>");
            writer.println("<div class='summary-grid'>");
            writer.println("<div class='summary-item'>");
            writer.println("<span class='summary-label'>Total Transaksi</span>");
            writer.println("<span class='summary-value'>" + totalTransactions + "</span>");
            writer.println("</div>");
            writer.println("<div class='summary-item'>");
            writer.println("<span class='summary-label'>Total Nilai</span>");
            writer.println("<span class='summary-value'>Rp " + String.format("%,.0f", totalAmount) + "</span>");
            writer.println("</div>");
            writer.println("<div class='summary-item'>");
            writer.println("<span class='summary-label'>Menunggu</span>");
            writer.println("<span class='summary-value'>" + pendingCount + "</span>");
            writer.println("</div>");
            writer.println("<div class='summary-item'>");
            writer.println("<span class='summary-label'>Diterima</span>");
            writer.println("<span class='summary-value'>" + approvedCount + "</span>");
            writer.println("</div>");
            writer.println("<div class='summary-item'>");
            writer.println("<span class='summary-label'>Ditolak</span>");
            writer.println("<span class='summary-value'>" + rejectedCount + "</span>");
            writer.println("</div>");
            writer.println("</div>");
            writer.println("</div>");
            
            // Print instruction
            writer.println("<div class='print-instruction'>");
            writer.println("<strong>📄 Untuk menyimpan sebagai PDF:</strong> Tekan Ctrl+P → Pilih 'Save as PDF' → Klik Save");
            writer.println("</div>");
            
            // Table
            writer.println("<table>");
            writer.println("<thead>");
            writer.println("<tr>");
            writer.println("<th style='width: 5%;'>ID</th>");
            writer.println("<th style='width: 20%;'>LAYANAN</th>");
            writer.println("<th style='width: 15%;'>PENGGUNA</th>");
            writer.println("<th style='width: 10%;'>JUMLAH</th>");
            writer.println("<th style='width: 8%;'>UNIT</th>");
            writer.println("<th style='width: 12%;'>TOTAL</th>");
            writer.println("<th style='width: 10%;'>STATUS</th>");
            writer.println("<th style='width: 20%;'>TANGGAL</th>");
            writer.println("</tr>");
            writer.println("</thead>");
            writer.println("<tbody>");
            
            // Reset and write data rows
            rs.beforeFirst();
            boolean hasData = false;
            while (rs.next()) {
                hasData = true;
                int id = rs.getInt("id");
                String serviceName = rs.getString("service_name");
                String userName = rs.getString("user_name");
                double amount = rs.getDouble("amount");
                int unit = rs.getInt("unit");
                double total = rs.getDouble("total");
                String status = rs.getString("status");
                String createdAt = rs.getString("created_at");
                
                String statusClass = "";
                String statusText = "";
                switch(status) {
                    case "menunggu":
                    case "pending":
                        statusClass = "status-pending";
                        statusText = "Menunggu";
                        break;
                    case "diterima":
                    case "completed":
                        statusClass = "status-approved";
                        statusText = "Diterima";
                        break;
                    case "ditolak":
                    case "cancelled":
                        statusClass = "status-rejected";
                        statusText = "Ditolak";
                        break;
                    default:
                        statusClass = "status-pending";
                        statusText = status;
                }
                
                writer.println("<tr>");
                writer.println("<td class='text-center'><strong>" + id + "</strong></td>");
                writer.println("<td>" + (serviceName != null ? serviceName : "<em>Layanan Tidak Ditemukan</em>") + "</td>");
                writer.println("<td>" + (userName != null ? userName : "<em>User Tidak Ditemukan</em>") + "</td>");
                writer.println("<td class='text-right currency'>Rp " + String.format("%,.0f", amount) + "</td>");
                writer.println("<td class='text-center'>" + unit + "</td>");
                writer.println("<td class='text-right currency'>Rp " + String.format("%,.0f", total) + "</td>");
                writer.println("<td class='text-center'><span class='" + statusClass + "'>" + statusText + "</span></td>");
                writer.println("<td class='text-center'>" + (createdAt != null ? createdAt.substring(0, Math.min(createdAt.length(), 16)) : "-") + "</td>");
                writer.println("</tr>");
            }
            
            if (!hasData) {
                writer.println("<tr>");
                writer.println("<td colspan='8' class='text-center' style='padding: 30px; color: #666; font-style: italic;'>Tidak ada data transaksi yang ditemukan</td>");
                writer.println("</tr>");
            }
            
            writer.println("</tbody>");
            writer.println("</table>");
            
            // Footer
            writer.println("<div class='footer'>");
            writer.println("<p><strong>Laporan ini dibuat secara otomatis oleh sistem Arqeta</strong></p>");
            writer.println("<p>© " + new SimpleDateFormat("yyyy").format(new Date()) + " PT. Arqeta Digital Solutions. Semua hak dilindungi.</p>");
            writer.println("</div>");
            
            writer.println("</div>");
            writer.println("</body>");
            writer.println("</html>");
            
            writer.flush();
            writer.close();
        }
        
        rs.close();
        ps.close();
        
    } catch (SQLException e) {
        session.setAttribute("notification", "Error saat mengekspor data: " + e.getMessage());
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=transaction");
    }
%>
