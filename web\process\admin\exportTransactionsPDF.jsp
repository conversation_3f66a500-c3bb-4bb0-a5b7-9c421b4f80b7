<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>
<%@page import="java.io.*"%>
<%@page import="java.text.SimpleDateFormat"%>
<%@page import="java.util.Date"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Check if user is logged in as admin
    String adminId = (String) session.getAttribute("adminId");
    if (adminId == null) {
        response.sendRedirect("../../form/signin.jsp");
        return;
    }

    String format = request.getParameter("format");
    if (format == null || (!format.equals("excel") && !format.equals("pdf"))) {
        session.setAttribute("notification", "Format ekspor tidak valid!");
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=transaction");
        return;
    }

    try {
        // Query untuk mengambil semua data transaksi
        String query = "SELECT t.*, u.name as user_name, u.email as user_email, s.name as service_name " +
                     "FROM transaction t " +
                     "LEFT JOIN user u ON t.user_id = u.id " +
                     "LEFT JOIN services s ON t.service_id = s.id " +
                     "ORDER BY t.id ASC";
        PreparedStatement ps = conn.prepareStatement(query);
        ResultSet rs = ps.executeQuery();

        String exportTimestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());

        if (format.equals("excel")) {
            // Export to Excel (CSV format)
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment; filename=\"data_transaksi_" + exportTimestamp + ".csv\"");
            
            PrintWriter writer = response.getWriter();
            
            // Write CSV header
            writer.println("ID,Nama Layanan,Pengguna,Email Pengguna,Jumlah,Unit,Total,Status,Tanggal Dibuat");
            
            // Write data rows
            while (rs.next()) {
                int id = rs.getInt("id");
                String serviceName = rs.getString("service_name");
                String userName = rs.getString("user_name");
                String userEmail = rs.getString("user_email");
                double amount = rs.getDouble("amount");
                int unit = rs.getInt("unit");
                double total = rs.getDouble("total");
                String status = rs.getString("status");
                String createdAt = rs.getString("created_at");
                
                // Convert status to readable text
                String statusText = "";
                switch(status) {
                    case "menunggu":
                    case "pending":
                        statusText = "Menunggu";
                        break;
                    case "diterima":
                    case "completed":
                        statusText = "Diterima";
                        break;
                    case "ditolak":
                    case "cancelled":
                        statusText = "Ditolak";
                        break;
                    default:
                        statusText = status;
                }
                
                // Escape commas and quotes in CSV
                serviceName = serviceName != null ? "\"" + serviceName.replace("\"", "\"\"") + "\"" : "\"Layanan Tidak Ditemukan\"";
                userName = userName != null ? "\"" + userName.replace("\"", "\"\"") + "\"" : "\"User Tidak Ditemukan\"";
                userEmail = userEmail != null ? "\"" + userEmail.replace("\"", "\"\"") + "\"" : "\"Email Tidak Ditemukan\"";
                
                writer.println(id + "," + serviceName + "," + userName + "," + userEmail + "," + 
                             String.format("%.0f", amount) + "," + unit + "," + 
                             String.format("%.0f", total) + ",\"" + statusText + "\",\"" + createdAt + "\"");
            }
            
            writer.flush();
            writer.close();
            
        } else if (format.equals("pdf")) {
            // Export to PDF (HTML format for download)
            response.setContentType("text/html; charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"laporan_transaksi_" + exportTimestamp + ".html\"");
            
            PrintWriter writer = response.getWriter();
            
            // Write HTML header with enhanced styling for PDF
            writer.println("<!DOCTYPE html>");
            writer.println("<html>");
            writer.println("<head>");
            writer.println("<meta charset='UTF-8'>");
            writer.println("<meta name='viewport' content='width=device-width, initial-scale=1.0'>");
            writer.println("<title>Laporan Data Transaksi - Arqeta</title>");
            writer.println("<style>");
            writer.println("* { margin: 0; padding: 0; box-sizing: border-box; }");
            writer.println("body { font-family: 'Arial', sans-serif; font-size: 12px; line-height: 1.4; color: #333; background: #fff; margin: 20px; }");
            writer.println(".header { text-align: center; margin-bottom: 30px; border-bottom: 3px solid #007bff; padding-bottom: 20px; }");
            writer.println(".header .company { font-size: 18px; font-weight: bold; color: #007bff; margin-bottom: 5px; }");
            writer.println(".header h1 { color: #333; font-size: 24px; margin: 10px 0; }");
            writer.println(".header .export-info { font-size: 12px; color: #666; }");
            writer.println(".summary { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px; border: 1px solid #dee2e6; }");
            writer.println(".summary h3 { color: #007bff; margin-bottom: 15px; font-size: 16px; }");
            writer.println(".summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; }");
            writer.println(".summary-item { background: white; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6; text-align: center; }");
            writer.println(".summary-item .label { font-weight: bold; color: #555; font-size: 11px; margin-bottom: 5px; }");
            writer.println(".summary-item .value { font-size: 18px; color: #007bff; font-weight: bold; }");
            writer.println("table { width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 11px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }");
            writer.println("th { background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 12px 8px; text-align: left; font-weight: bold; border: 1px solid #0056b3; }");
            writer.println("td { padding: 10px 8px; border: 1px solid #dee2e6; vertical-align: top; }");
            writer.println("tr:nth-child(even) { background-color: #f8f9fa; }");
            writer.println("tr:hover { background-color: #e3f2fd; }");
            writer.println(".status-pending { background: #fff3cd; color: #856404; padding: 4px 8px; border-radius: 12px; font-size: 10px; font-weight: bold; }");
            writer.println(".status-approved { background: #d4edda; color: #155724; padding: 4px 8px; border-radius: 12px; font-size: 10px; font-weight: bold; }");
            writer.println(".status-rejected { background: #f8d7da; color: #721c24; padding: 4px 8px; border-radius: 12px; font-size: 10px; font-weight: bold; }");
            writer.println(".currency { font-weight: bold; color: #28a745; }");
            writer.println(".footer { margin-top: 40px; text-align: center; font-size: 10px; color: #666; border-top: 2px solid #dee2e6; padding-top: 20px; }");
            writer.println(".no-data { text-align: center; padding: 40px; color: #666; font-style: italic; background: #f8f9fa; border-radius: 8px; }");
            writer.println("@media print { ");
            writer.println("  body { margin: 0; font-size: 10px; }");
            writer.println("  .header { margin-bottom: 20px; }");
            writer.println("  table { font-size: 9px; }");
            writer.println("  th, td { padding: 6px 4px; }");
            writer.println("  .no-print { display: none; }");
            writer.println("}");
            writer.println("</style>");
            writer.println("</head>");
            writer.println("<body>");
            
            // Header section
            writer.println("<div class='header'>");
            writer.println("<div class='company'>PT. ARQETA DIGITAL SOLUTIONS</div>");
            writer.println("<h1>LAPORAN DATA TRANSAKSI</h1>");
            writer.println("<div class='export-info'>Diekspor pada: " + new SimpleDateFormat("dd MMMM yyyy, HH:mm:ss").format(new Date()) + " WIB</div>");
            writer.println("</div>");
            
            // Calculate summary statistics first
            int totalRecords = 0;
            double totalAmount = 0;
            int pendingCount = 0, approvedCount = 0, rejectedCount = 0;
            
            while (rs.next()) {
                totalRecords++;
                totalAmount += rs.getDouble("total");
                String status = rs.getString("status");
                switch(status) {
                    case "menunggu":
                    case "pending":
                        pendingCount++;
                        break;
                    case "diterima":
                    case "completed":
                        approvedCount++;
                        break;
                    case "ditolak":
                    case "cancelled":
                        rejectedCount++;
                        break;
                }
            }
            
            // Summary section
            writer.println("<div class='summary'>");
            writer.println("<h3>Ringkasan Transaksi</h3>");
            writer.println("<div class='summary-grid'>");
            writer.println("<div class='summary-item'>");
            writer.println("<div class='label'>TOTAL TRANSAKSI</div>");
            writer.println("<div class='value'>" + totalRecords + "</div>");
            writer.println("</div>");
            writer.println("<div class='summary-item'>");
            writer.println("<div class='label'>TOTAL NILAI</div>");
            writer.println("<div class='value'>Rp " + String.format("%,.0f", totalAmount) + "</div>");
            writer.println("</div>");
            writer.println("<div class='summary-item'>");
            writer.println("<div class='label'>MENUNGGU</div>");
            writer.println("<div class='value'>" + pendingCount + "</div>");
            writer.println("</div>");
            writer.println("<div class='summary-item'>");
            writer.println("<div class='label'>DITERIMA</div>");
            writer.println("<div class='value'>" + approvedCount + "</div>");
            writer.println("</div>");
            writer.println("<div class='summary-item'>");
            writer.println("<div class='label'>DITOLAK</div>");
            writer.println("<div class='value'>" + rejectedCount + "</div>");
            writer.println("</div>");
            writer.println("</div>");
            writer.println("</div>");
            
            // Reset ResultSet for data display
            rs.close();
            ps.close();
            ps = conn.prepareStatement(query);
            rs = ps.executeQuery();
            
            // Data table
            writer.println("<table>");
            writer.println("<thead>");
            writer.println("<tr>");
            writer.println("<th style='width: 5%;'>ID</th>");
            writer.println("<th style='width: 20%;'>NAMA LAYANAN</th>");
            writer.println("<th style='width: 15%;'>PENGGUNA</th>");
            writer.println("<th style='width: 15%;'>EMAIL</th>");
            writer.println("<th style='width: 10%;'>JUMLAH</th>");
            writer.println("<th style='width: 5%;'>UNIT</th>");
            writer.println("<th style='width: 12%;'>TOTAL</th>");
            writer.println("<th style='width: 8%;'>STATUS</th>");
            writer.println("<th style='width: 10%;'>TANGGAL</th>");
            writer.println("</tr>");
            writer.println("</thead>");
            writer.println("<tbody>");
            
            // Write data rows
            boolean hasData = false;
            while (rs.next()) {
                hasData = true;
                int id = rs.getInt("id");
                String serviceName = rs.getString("service_name");
                String userName = rs.getString("user_name");
                String userEmail = rs.getString("user_email");
                double amount = rs.getDouble("amount");
                int unit = rs.getInt("unit");
                double total = rs.getDouble("total");
                String status = rs.getString("status");
                String createdAt = rs.getString("created_at");
                
                // Convert status to readable text and CSS class
                String statusText = "";
                String statusClass = "";
                switch(status) {
                    case "menunggu":
                    case "pending":
                        statusText = "Menunggu";
                        statusClass = "status-pending";
                        break;
                    case "diterima":
                    case "completed":
                        statusText = "Diterima";
                        statusClass = "status-approved";
                        break;
                    case "ditolak":
                    case "cancelled":
                        statusText = "Ditolak";
                        statusClass = "status-rejected";
                        break;
                    default:
                        statusText = status;
                        statusClass = "status-pending";
                }
                
                writer.println("<tr>");
                writer.println("<td><strong>" + id + "</strong></td>");
                writer.println("<td>" + (serviceName != null ? serviceName : "<em>Layanan Tidak Ditemukan</em>") + "</td>");
                writer.println("<td>" + (userName != null ? userName : "<em>User Tidak Ditemukan</em>") + "</td>");
                writer.println("<td>" + (userEmail != null ? userEmail : "<em>Email Tidak Ditemukan</em>") + "</td>");
                writer.println("<td><span class='currency'>Rp " + String.format("%,.0f", amount) + "</span></td>");
                writer.println("<td><strong>" + unit + "</strong></td>");
                writer.println("<td><span class='currency'>Rp " + String.format("%,.0f", total) + "</span></td>");
                writer.println("<td><span class='" + statusClass + "'>" + statusText + "</span></td>");
                writer.println("<td>" + (createdAt != null ? createdAt.substring(0, Math.min(createdAt.length(), 16)) : "") + "</td>");
                writer.println("</tr>");
            }
            
            // If no data found
            if (!hasData) {
                writer.println("<tr>");
                writer.println("<td colspan='9' class='no-data'>Tidak ada data transaksi yang ditemukan</td>");
                writer.println("</tr>");
            }
            
            writer.println("</tbody>");
            writer.println("</table>");
            
            // Footer
            writer.println("<div class='footer'>");
            writer.println("<p><strong>Laporan ini dibuat secara otomatis oleh sistem Arqeta</strong></p>");
            writer.println("<p>© " + new SimpleDateFormat("yyyy").format(new Date()) + " PT. Arqeta Digital Solutions. Semua hak dilindungi.</p>");
            writer.println("<p><em>Untuk mencetak sebagai PDF: Tekan Ctrl+P dan pilih 'Save as PDF'</em></p>");
            writer.println("</div>");
            
            writer.println("</body>");
            writer.println("</html>");
            
            writer.flush();
            writer.close();
        }
        
        rs.close();
        ps.close();
        
    } catch (SQLException e) {
        session.setAttribute("notification", "Error saat mengekspor data: " + e.getMessage());
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=transaction");
    }
%>
