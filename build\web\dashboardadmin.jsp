<%--
    Document    : dashboardadmin
    Created on  : May 30, 2025, 3:44:05 PM
    Author      : Arqeta
    Description : Admin dashboard page for Arqeta website
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="config/connection.jsp" %>

<%
    // Check if user is logged in as admin
    String adminId = (String) session.getAttribute("adminId");
    if (adminId == null) {
        response.sendRedirect("form/signin.jsp");
        return;
    }

    // Get admin details
    String adminName = "";
    boolean isGoogleAdmin = false;
    String adminPhotoUrl = "";
    try {
        // Get admin name
        PreparedStatement ps = conn.prepareStatement("SELECT name FROM admin WHERE id = ?");
        ps.setString(1, adminId);
        ResultSet rs = ps.executeQuery();

        if (rs.next()) {
            adminName = rs.getString("name");
        }
        rs.close();
        ps.close();

        // Check if admin has Google account
        PreparedStatement ps2 = conn.prepareStatement("SELECT photo_url FROM admin_google WHERE admin_id = ?");
        ps2.setString(1, adminId);
        ResultSet rs2 = ps2.executeQuery();

        if (rs2.next()) {
            isGoogleAdmin = true;
            adminPhotoUrl = rs2.getString("photo_url");
        }
        rs2.close();
        ps2.close();
    } catch (SQLException e) {
        out.println("Error: " + e.getMessage());
    }

    // Get active page
    String activePage = request.getParameter("page");
    if (activePage == null) {
        activePage = "beranda";
    }
%>

<!DOCTYPE html>
<html lang="id">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="Dashboard Admin Arqeta">
        <title>Dashboard Admin | Arqeta</title>

        <link rel="stylesheet" href="dist/css/dashboard.css">
        <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600&display=swap" rel="stylesheet">
        <script src="https://unpkg.com/feather-icons"></script>

        <!-- Firebase scripts for Google authentication -->
        <script src="https://www.gstatic.com/firebasejs/9.19.1/firebase-app-compat.js"></script>
        <script src="https://www.gstatic.com/firebasejs/9.19.1/firebase-auth-compat.js"></script>

        <script>
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                // Using custom notification instead of alert()
                showNotification('Klik kanan dinonaktifkan pada situs ini!', 'error');
            });

            // Prevent inspect element
            document.addEventListener('keydown', function(e) {
                if (e.keyCode === 123 || (e.ctrlKey && e.shiftKey && e.keyCode === 73)) {
                    e.preventDefault();
                    // Using custom notification instead of alert()
                    showNotification('Inspeksi elemen dinonaktifkan pada situs ini!', 'error');
                }
            });
        </script>
    </head>
    <body>
        <div class="dashboard-container">
            <div class="sidebar" id="sidebar">
                <div class="sidebar-header">
                    <h2>Arqeta</h2>
                    <button id="sidebarToggle" class="sidebar-toggle">
                        <i data-feather="menu"></i>
                    </button>
                </div>

                <div class="sidebar-content">
                    <div class="admin-info">
                        <div class="admin-avatar">
                            <% if (isGoogleAdmin && adminPhotoUrl != null && !adminPhotoUrl.isEmpty()) { %>
                                <img src="<%= adminPhotoUrl %>" alt="<%= adminName %>" onerror="this.src='../dist/img/default-avatar.png';">
                            <% } else { %>
                                <i data-feather="user"></i>
                            <% } %>
                        </div>
                        <div class="admin-details">
                            <h3><%= adminName %></h3>
                            <p>Admin</p>
                        </div>
                    </div>

                    <nav class="sidebar-menu">
                        <ul>
                            <li class="<%= activePage.equals("beranda") ? "active" : "" %>">
                                <a href="?page=beranda">
                                    <span class="menu-icon"><i data-feather="home"></i></span>
                                    <span class="menu-text">Beranda</span>
                                </a>
                            </li>
                            <li class="<%= activePage.equals("register") ? "active" : "" %>">
                                <a href="?page=register">
                                    <span class="menu-icon"><i data-feather="users"></i></span>
                                    <span class="menu-text">Data Register</span>
                                </a>
                            </li>
                            <li class="<%= activePage.equals("google_accounts") ? "active" : "" %>">
                                <a href="?page=google_accounts">
                                    <span class="menu-icon"><i data-feather="shield"></i></span>
                                    <span class="menu-text">Data Akun Google</span>
                                </a>
                            </li>
                            <li class="<%= activePage.equals("services") ? "active" : "" %>">
                                <a href="?page=services">
                                    <span class="menu-icon"><i data-feather="package"></i></span>
                                    <span class="menu-text">Data Layanan</span>
                                </a>
                            </li>
                            <li class="<%= activePage.equals("transaction") ? "active" : "" %>">
                                <a href="?page=transaction">
                                    <span class="menu-icon"><i data-feather="credit-card"></i></span>
                                    <span class="menu-text">Data Transaksi</span>
                                </a>
                            </li>
                            <li class="<%= activePage.equals("portfolio") ? "active" : "" %>">
                                <a href="?page=portfolio">
                                    <span class="menu-icon"><i data-feather="briefcase"></i></span>
                                    <span class="menu-text">Data Portfolio</span>
                                </a>
                            </li>
                            <li class="<%= activePage.equals("blog") ? "active" : "" %>">
                                <a href="?page=blog">
                                    <span class="menu-icon"><i data-feather="file-text"></i></span>
                                    <span class="menu-text">Data Blog</span>
                                </a>
                            </li>
                            <li class="<%= activePage.equals("contact") ? "active" : "" %>">
                                <a href="?page=contact">
                                    <span class="menu-icon"><i data-feather="message-square"></i></span>
                                    <span class="menu-text">Data Kontak</span>
                                </a>
                            </li>
                            <li class="<%= activePage.equals("otp") ? "active" : "" %>">
                                <a href="?page=otp">
                                    <span class="menu-icon"><i data-feather="key"></i></span>
                                    <span class="menu-text">Data Riwayat OTP</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>

                <div class="sidebar-footer">
                    <div class="theme-toggle-wrapper">
                        <span>Mode Gelap</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="themeSwitchSidebar">
                            <span class="slider round"></span>
                        </label>
                    </div>
                    <a href="process/logoutProcess.jsp" class="logout-btn">
                        <i data-feather="log-out"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </div>

            <div class="main-content">
                <header class="top-navbar">
                    <div class="navbar-left">
                        <button id="mobileMenuToggle" class="mobile-menu-toggle">
                            <i data-feather="menu"></i>
                        </button>
                        <h2 class="page-title">
                            <%= activePage.equals("beranda") ? "Beranda" :
                               activePage.equals("register") ? "Data Register" :
                               activePage.equals("google_accounts") ? "Data Akun Google" :
                               activePage.equals("services") ? "Data Layanan" :
                               activePage.equals("transaction") ? "Data Transaksi" :
                               activePage.equals("portfolio") ? "Data Portfolio" :
                               activePage.equals("blog") ? "Data Blog" :
                               activePage.equals("contact") ? "Data Kontak" :
                               activePage.equals("otp") ? "Data Riwayat OTP" : "Dashboard"
                            %>
                        </h2>
                    </div>
                    <div class="navbar-right">
                        <!-- Mengubah dari icon statis menjadi indikator tema yang dinamis -->
                        <div id="themeIndicator" class="theme-indicator" title="Mode Tema">
                            <i data-feather="sun" class="feather-sun"></i>
                            <i data-feather="moon" class="feather-moon"></i>
                        </div>
                    </div>
                </header>

                <div class="content">
                    <% if (activePage.equals("beranda")) { %>
                        <div class="dashboard-stats">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i data-feather="users"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>
                                        <%
                                            try {
                                                PreparedStatement ps = conn.prepareStatement("SELECT COUNT(*) AS total FROM admin");
                                                ResultSet rs = ps.executeQuery();
                                                rs.next();
                                                out.print(rs.getInt("total"));
                                                rs.close();
                                                ps.close();
                                            } catch (SQLException e) {
                                                out.print("0");
                                            }
                                        %>
                                    </h3>
                                    <p>Admin</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i data-feather="user"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>
                                        <%
                                            try {
                                                PreparedStatement ps = conn.prepareStatement("SELECT COUNT(*) AS total FROM user");
                                                ResultSet rs = ps.executeQuery();
                                                rs.next();
                                                out.print(rs.getInt("total"));
                                                rs.close();
                                                ps.close();
                                            } catch (SQLException e) {
                                                out.print("0");
                                            }
                                        %>
                                    </h3>
                                    <p>Pengguna</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i data-feather="shield"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>
                                        <%
                                            try {
                                                PreparedStatement ps = conn.prepareStatement("SELECT COUNT(*) AS total FROM admin_google UNION ALL SELECT COUNT(*) AS total FROM user_google");
                                                ResultSet rs = ps.executeQuery();
                                                int totalGoogle = 0;
                                                while (rs.next()) {
                                                    totalGoogle += rs.getInt("total");
                                                }
                                                out.print(totalGoogle);
                                                rs.close();
                                                ps.close();
                                            } catch (SQLException e) {
                                                out.print("0");
                                            }
                                        %>
                                    </h3>
                                    <p>Akun Google</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i data-feather="package"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>
                                        <%
                                            try {
                                                PreparedStatement ps = conn.prepareStatement("SELECT COUNT(*) AS total FROM services");
                                                ResultSet rs = ps.executeQuery();
                                                rs.next();
                                                out.print(rs.getInt("total"));
                                                rs.close();
                                                ps.close();
                                            } catch (SQLException e) {
                                                out.print("0");
                                            }
                                        %>
                                    </h3>
                                    <p>Layanan</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i data-feather="credit-card"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>
                                        <%
                                            try {
                                                PreparedStatement ps = conn.prepareStatement("SELECT COUNT(*) AS total FROM transaction");
                                                ResultSet rs = ps.executeQuery();
                                                rs.next();
                                                out.print(rs.getInt("total"));
                                                rs.close();
                                                ps.close();
                                            } catch (SQLException e) {
                                                out.print("0");
                                            }
                                        %>
                                    </h3>
                                    <p>Transaksi</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i data-feather="briefcase"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>
                                        <%
                                            try {
                                                PreparedStatement ps = conn.prepareStatement("SELECT COUNT(*) AS total FROM portfolio");
                                                ResultSet rs = ps.executeQuery();
                                                rs.next();
                                                out.print(rs.getInt("total"));
                                                rs.close();
                                                ps.close();
                                            } catch (SQLException e) {
                                                out.print("0");
                                            }
                                        %>
                                    </h3>
                                    <p>Portfolio</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i data-feather="file-text"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>
                                        <%
                                            try {
                                                PreparedStatement ps = conn.prepareStatement("SELECT COUNT(*) AS total FROM blog");
                                                ResultSet rs = ps.executeQuery();
                                                rs.next();
                                                out.print(rs.getInt("total"));
                                                rs.close();
                                                ps.close();
                                            } catch (SQLException e) {
                                                out.print("0");
                                            }
                                        %>
                                    </h3>
                                    <p>Blog</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i data-feather="message-square"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>
                                        <%
                                            try {
                                                PreparedStatement ps = conn.prepareStatement("SELECT COUNT(*) AS total FROM contact WHERE status = 'unread'");
                                                ResultSet rs = ps.executeQuery();
                                                rs.next();
                                                out.print(rs.getInt("total"));
                                                rs.close();
                                                ps.close();
                                            } catch (SQLException e) {
                                                out.print("0");
                                            }
                                        %>
                                    </h3>
                                    <p>Pesan Belum Dibaca</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i data-feather="key"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>
                                        <%
                                            try {
                                                PreparedStatement ps = conn.prepareStatement("SELECT COUNT(*) AS total FROM otp_log");
                                                ResultSet rs = ps.executeQuery();
                                                rs.next();
                                                out.print(rs.getInt("total"));
                                                rs.close();
                                                ps.close();
                                            } catch (SQLException e) {
                                                out.print("0");
                                            }
                                        %>
                                    </h3>
                                    <p>Riwayat OTP</p>
                                </div>
                            </div>
                        </div>

                        <div class="dashboard-section">
                            <div class="section-header">
                                <h3>Pesan Terbaru</h3>
                                <a href="?page=contact" class="view-all">Lihat Semua</a>
                            </div>
                            <div class="table-responsive">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>Nama</th>
                                            <th>Email</th>
                                            <th>Subjek</th>
                                            <th>Status</th>
                                            <th>Waktu</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <%
                                            try {
                                                PreparedStatement ps = conn.prepareStatement("SELECT * FROM contact ORDER BY created_at DESC LIMIT 5");
                                                ResultSet rs = ps.executeQuery();

                                                while (rs.next()) {
                                        %>
                                        <tr>
                                            <td><%= rs.getString("name") %></td>
                                            <td><%= rs.getString("email") %></td>
                                            <td><%= rs.getString("subject") %></td>
                                            <td>
                                                <span class="status-badge <%= rs.getString("status").equals("unread") ? "status-unread" : "status-read" %>">
                                                    <%= rs.getString("status").equals("unread") ? "Belum dibaca" : "Dibaca" %>
                                                </span>
                                            </td>
                                            <td><%= rs.getTimestamp("created_at") %></td>
                                        </tr>
                                        <%
                                                }
                                                rs.close();
                                                ps.close();
                                            } catch (SQLException e) {
                                                out.println("<tr><td colspan='5'>Error: " + e.getMessage() + "</td></tr>");
                                            }
                                        %>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <% } else if (activePage.equals("register")) { %>
                        <jsp:include page="form/admin/signupdata.jsp" />
                    <% } else if (activePage.equals("google_accounts")) { %>
                        <jsp:include page="form/admin/googleaccountdata.jsp" />
                    <% } else if (activePage.equals("services")) { %>
                        <jsp:include page="form/admin/servicesdata.jsp" />
                    <% } else if (activePage.equals("transaction")) { %>
                        <jsp:include page="form/admin/transactiondata.jsp" />
                    <% } else if (activePage.equals("portfolio")) { %>
                        <jsp:include page="form/admin/portfoliodata.jsp" />
                    <% } else if (activePage.equals("blog")) { %>
                        <jsp:include page="form/admin/blogdata.jsp" />
                    <% } else if (activePage.equals("contact")) { %>
                        <jsp:include page="form/admin/contactdata.jsp" />
                    <% } else if (activePage.equals("otp")) { %>
                        <jsp:include page="form/admin/otplogdata.jsp" />
                    <% } %>
                </div>
            </div>
        </div>

        <div class="notification" id="notification">
            <div class="notification-content">
                <span class="notification-message" id="notificationMessage"></span>
                <span class="close-notification" onclick="closeNotification()">&times;</span>
            </div>
        </div>

        <!-- Hidden inputs untuk session notifications -->
        <%
            String sessionNotification = (String) session.getAttribute("notification");
            String sessionNotificationType = (String) session.getAttribute("notificationType");
            if (sessionNotification != null) {
                // Clear session attributes after reading
                session.removeAttribute("notification");
                session.removeAttribute("notificationType");
        %>
        <input type="hidden" id="sessionMessage" value="<%= sessionNotification %>">
        <input type="hidden" id="messageType" value="<%= sessionNotificationType != null ? sessionNotificationType : "info" %>">
        <%
            }
        %>

        <!-- Add Account Modal -->
        <div id="addAccountModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="addAccountTitle">Tambah Akun</h3>
                    <span class="close-modal" onclick="closeModal('addAccountModal')">&times;</span>
                </div>
                <form id="addAccountForm" method="POST">
                    <input type="hidden" id="accountType" name="type" value="admin">
                    <div class="form-group">
                        <label for="addName">Nama</label>
                        <input type="text" id="addName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="addUsername">Username</label>
                        <input type="text" id="addUsername" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="addEmail">Email</label>
                        <input type="email" id="addEmail" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="addPassword">Kata Sandi</label>
                        <div class="password-input">
                            <input type="password" id="addPassword" name="password" required>
                            <button type="button" class="toggle-password" onclick="togglePassword('addPassword')">
                                <i data-feather="eye-off"></i>
                            </button>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="btn-secondary" onclick="closeModal('addAccountModal')">Batal</button>
                        <button type="submit" class="btn-primary">Tambah</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Edit Account Modal -->
        <div id="editAccountModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="editAccountTitle">Edit Akun</h3>
                    <span class="close-modal" onclick="closeModal('editAccountModal')">&times;</span>
                </div>
                <form id="editAccountForm" method="POST">
                    <input type="hidden" id="editAccountId" name="id">
                    <input type="hidden" id="editAccountType" name="type" value="admin">
                    <div class="form-group">
                        <label for="editName">Nama</label>
                        <input type="text" id="editName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="editUsername">Username</label>
                        <input type="text" id="editUsername" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="editEmail">Email</label>
                        <input type="email" id="editEmail" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="editPassword">Kata Sandi Baru (kosongkan jika tidak ingin mengubah)</label>
                        <div class="password-input">
                            <input type="password" id="editPassword" name="password">
                            <button type="button" class="toggle-password" onclick="togglePassword('editPassword')">
                                <i data-feather="eye-off"></i>
                            </button>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="btn-secondary" onclick="closeModal('editAccountModal')">Batal</button>
                        <button type="submit" class="btn-primary">Update</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div id="deleteModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Konfirmasi Hapus</h3>
                    <span class="close-modal" onclick="closeModal('deleteModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <p>Apakah Anda yakin ingin menghapus data ini?</p>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn-secondary" onclick="closeModal('deleteModal')">Batal</button>
                    <button type="button" class="btn-danger" onclick="confirmDelete()">Hapus</button>
                </div>
            </div>
        </div>

        <button id="backToTopBtn" class="back-to-top">
            <i data-feather="arrow-up"></i>
        </button>

        <script src="dist/js/dashboard.js"></script>
        <script>
            // Feather icons
            document.addEventListener('DOMContentLoaded', function() {
                feather.replace();

                // Inisialisasi tema dari localStorage
                initializeTheme();
            });

            // Toggle sidebar
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');

            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                // Simpan status sidebar ke localStorage
                localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
            });

            mobileMenuToggle.addEventListener('click', function() {
                sidebar.classList.toggle('mobile-active');

                // Add body scroll lock when mobile menu is open
                if (sidebar.classList.contains('mobile-active')) {
                    document.body.style.overflow = 'hidden';
                } else {
                    document.body.style.overflow = '';
                }
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', function(event) {
                if (window.innerWidth <= 767) {
                    const isClickInsideSidebar = sidebar.contains(event.target);
                    const isClickOnToggle = mobileMenuToggle.contains(event.target);

                    if (!isClickInsideSidebar && !isClickOnToggle && sidebar.classList.contains('mobile-active')) {
                        sidebar.classList.remove('mobile-active');
                        document.body.style.overflow = '';
                    }
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 767) {
                    sidebar.classList.remove('mobile-active');
                    document.body.style.overflow = '';
                }
            });

            // Inisialisasi status sidebar dari localStorage
            if(localStorage.getItem('sidebarCollapsed') === 'true') {
                sidebar.classList.add('collapsed');
            }

            // Toggle tema
            function initializeTheme() {
                const themeSwitchSidebar = document.getElementById('themeSwitchSidebar');
                const themeIndicator = document.getElementById('themeIndicator');
                const body = document.body;
                const isDarkMode = localStorage.getItem('darkMode') === 'true';

                // Set status toggle dan tema sesuai localStorage
                if (isDarkMode) {
                    body.classList.add('dark-mode');
                    themeSwitchSidebar.checked = true;
                    updateThemeIcons(true);
                } else {
                    body.classList.remove('dark-mode');
                    themeSwitchSidebar.checked = false;
                    updateThemeIcons(false);
                }

                // Event listener untuk toggle switch di sidebar
                themeSwitchSidebar.addEventListener('change', function() {
                    toggleTheme(this.checked);
                });

                // Event listener untuk indikator tema di navbar
                themeIndicator.addEventListener('click', function() {
                    const currentDarkMode = body.classList.contains('dark-mode');
                    themeSwitchSidebar.checked = !currentDarkMode;
                    toggleTheme(!currentDarkMode);
                });
            }

            // Fungsi untuk toggle tema
            function toggleTheme(isDark) {
                const body = document.body;

                if (isDark) {
                    body.classList.add('dark-mode');
                    localStorage.setItem('darkMode', 'true');
                } else {
                    body.classList.remove('dark-mode');
                    localStorage.setItem('darkMode', 'false');
                }
                updateThemeIcons(isDark);
            }

            // Fungsi untuk memperbarui ikon tema
            function updateThemeIcons(isDark) {
                const themeIndicator = document.getElementById('themeIndicator');
                if (themeIndicator) {
                    const sunIcon = themeIndicator.querySelector('.feather-sun');
                    const moonIcon = themeIndicator.querySelector('.feather-moon');
                    if (isDark) {
                        if (sunIcon) sunIcon.style.display = 'none';
                        if (moonIcon) moonIcon.style.display = 'inline-block';
                    } else {
                        if (sunIcon) sunIcon.style.display = 'inline-block';
                        if (moonIcon) moonIcon.style.display = 'none';
                    }
                }
            }

            // Back to top button
            const backToTopBtn = document.getElementById('backToTopBtn');
            window.addEventListener('scroll', function() {
                if (window.scrollY > 300) {
                    backToTopBtn.classList.add('active');
                } else {
                    backToTopBtn.classList.remove('active');
                }
            });

            if (backToTopBtn) {
                backToTopBtn.addEventListener('click', function() {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                });
            }

            // Initialize Firebase when page loads
            document.addEventListener('DOMContentLoaded', function() {
                // Initialize Firebase for Google authentication
                if (typeof firebase !== 'undefined' && !firebase.apps.length) {
                    const firebaseConfig = {
                        apiKey: "AIzaSyDF1xa0UEu0wTCYzDPdC0AFJKY5Y5PjBhc",
                        authDomain: "thearqeta.firebaseapp.com",
                        projectId: "thearqeta",
                        storageBucket: "thearqeta.firebasestorage.app",
                        messagingSenderId: "560559961893",
                        appId: "1:560559961893:web:6d51f4b364c763bfcc4ccc",
                        measurementId: "G-BPLQBHFFX1"
                    };
                    firebase.initializeApp(firebaseConfig);
                    console.log('Firebase initialized in dashboard');
                }

                // Show notification if there's a message in session
                <%
                    String notificationMessage = (String) session.getAttribute("message");
                    String notificationType = (String) session.getAttribute("messageType");

                    if (notificationMessage != null && !notificationMessage.isEmpty()) {
                        session.removeAttribute("message");
                        session.removeAttribute("messageType");
                %>
                    showNotification('<%= notificationMessage %>', '<%= notificationType != null ? notificationType : "info" %>');
                <% } %>
            });

            // Notification functions
            function showNotification(message, type = 'info') {
                const notification = document.getElementById('notification');
                const notificationMessage = document.getElementById('notificationMessage');

                notificationMessage.textContent = message;
                notification.className = 'notification';
                notification.classList.add(`notification-${type}`);
                notification.classList.add('show');

                // Auto hide after 5 seconds
                setTimeout(() => {
                    closeNotification();
                }, 5000);
            }

            function closeNotification() {
                const notification = document.getElementById('notification');
                notification.classList.remove('show');
            }

            // Prevent copy paste
            document.addEventListener('copy', function(e) {
                e.preventDefault();
                showNotification('Maaf, tindakan salin telah dinonaktifkan di situs ini!', 'error');
            });

            document.addEventListener('paste', function(e) {
                e.preventDefault();
                showNotification('Maaf, tindakan tempel telah dinonaktifkan di situs ini!', 'error');
            });

            // Global variables for data management
            let deleteId = null;
            let deleteType = null;

            function openAddAccountModal() {
                // Determine current type from URL parameters or default to admin
                const urlParams = new URLSearchParams(window.location.search);
                const currentType = urlParams.get('type') || 'admin';

                document.getElementById('accountType').value = currentType;
                document.getElementById('addAccountTitle').textContent = 'Tambah ' + (currentType === 'admin' ? 'Admin' : 'User');
                document.getElementById('addAccountForm').action = 'process/admin/addAccount.jsp';
                document.getElementById('addAccountModal').style.display = 'block';
            }

            // Note: openAddGoogleAccountModal function is defined in googleaccountdata.jsp
            // when the Google accounts page is loaded

            function openEditAccountModal(id, name, username, email, type) {
                document.getElementById('editAccountId').value = id;
                document.getElementById('editAccountType').value = type;
                document.getElementById('editName').value = name;
                document.getElementById('editUsername').value = username;
                document.getElementById('editEmail').value = email;
                document.getElementById('editPassword').value = '';
                document.getElementById('editAccountTitle').textContent = 'Edit ' + (type === 'admin' ? 'Admin' : 'User');
                document.getElementById('editAccountForm').action = 'process/admin/editAccount.jsp';
                document.getElementById('editAccountModal').style.display = 'block';
            }

            function openDeleteModal(id, type) {
                deleteId = id;
                deleteType = type;
                document.getElementById('deleteModal').style.display = 'block';
            }

            function openDeleteGoogleModal(id, type) {
                deleteId = id;
                deleteType = type + '_google';
                document.getElementById('deleteModal').style.display = 'block';
            }

            function confirmDelete() {
                if (deleteId && deleteType) {
                    const form = document.createElement('form');
                    form.method = 'POST';

                    // Determine the correct action based on delete type
                    if (deleteType.includes('_google')) {
                        form.action = 'process/admin/deleteGoogleAccount.jsp';
                        const actualType = deleteType.replace('_google', '');

                        const idInput = document.createElement('input');
                        idInput.type = 'hidden';
                        idInput.name = 'id';
                        idInput.value = deleteId;

                        const typeInput = document.createElement('input');
                        typeInput.type = 'hidden';
                        typeInput.name = 'type';
                        typeInput.value = actualType;

                        form.appendChild(idInput);
                        form.appendChild(typeInput);
                    } else {
                        form.action = 'process/admin/deleteAccount.jsp';

                        const idInput = document.createElement('input');
                        idInput.type = 'hidden';
                        idInput.name = 'id';
                        idInput.value = deleteId;

                        const typeInput = document.createElement('input');
                        typeInput.type = 'hidden';
                        typeInput.name = 'type';
                        typeInput.value = deleteType;

                        form.appendChild(idInput);
                        form.appendChild(typeInput);
                    }

                    document.body.appendChild(form);
                    form.submit();
                }
            }

            function closeModal(modalId) {
                document.getElementById(modalId).style.display = 'none';
                // Reset form if it exists
                const form = document.querySelector('#' + modalId + ' form');
                if (form) {
                    form.reset();
                }
            }

            function togglePassword(inputId) {
                const input = document.getElementById(inputId);
                const icon = input.nextElementSibling.querySelector('i');

                if (input.type === 'password') {
                    input.type = 'text';
                    icon.setAttribute('data-feather', 'eye');
                } else {
                    input.type = 'password';
                    icon.setAttribute('data-feather', 'eye-off');
                }
                feather.replace();
            }


        </script>
    </body>
</html>
