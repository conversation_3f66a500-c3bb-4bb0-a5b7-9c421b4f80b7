<%--
    Document   : exportServices
    Created on : July 30, 2025
    Author     : Arqeta
    Description: Export services data to Word and JSON formats
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>
<%@page import="java.io.*"%>
<%@page import="java.text.SimpleDateFormat"%>
<%@page import="java.util.Date"%>
<%@page import="java.util.ArrayList"%>
<%@page import="java.util.HashMap"%>
<%@page import="java.util.Map"%>
<%@page import="java.util.Base64"%>
<%@page import="java.nio.file.Files"%>
<%@page import="java.nio.file.Paths"%>
<%@page import="java.nio.file.Path"%>
<%@include file="../../config/connection.jsp" %>

<%!
    // Helper method to convert image to base64
    private String getImageAsBase64(String imagePath, ServletContext context) {
        try {
            String realPath = context.getRealPath("/assets/images/services/" + imagePath);
            Path path = Paths.get(realPath);
            if (Files.exists(path)) {
                byte[] imageBytes = Files.readAllBytes(path);
                String base64 = Base64.getEncoder().encodeToString(imageBytes);

                // Determine MIME type based on file extension
                String mimeType = "image/jpeg"; // default
                String extension = imagePath.toLowerCase();
                if (extension.endsWith(".png")) {
                    mimeType = "image/png";
                } else if (extension.endsWith(".gif")) {
                    mimeType = "image/gif";
                } else if (extension.endsWith(".webp")) {
                    mimeType = "image/webp";
                }

                return "data:" + mimeType + ";base64," + base64;
            }
        } catch (Exception e) {
            // Log error but don't break the export
            System.err.println("Error converting image to base64: " + e.getMessage());
        }
        return null;
    }
%>

<%
    // Check if user is logged in as admin
    String adminId = (String) session.getAttribute("adminId");
    if (adminId == null) {
        response.sendRedirect("../../form/signin.jsp");
        return;
    }

    String format = request.getParameter("format");
    if (format == null || (!format.equals("word") && !format.equals("json"))) {
        session.setAttribute("notification", "Format ekspor tidak valid!");
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=services");
        return;
    }

    try {
        // Query untuk mengambil semua data layanan
        String query = "SELECT * FROM services ORDER BY id ASC";
        PreparedStatement ps = conn.prepareStatement(query);
        ResultSet rs = ps.executeQuery();

        if (format.equals("word")) {
            // Export to Word (HTML format that can be opened in Word)
            response.setContentType("application/msword");
            response.setHeader("Content-Disposition", "attachment; filename=\"data_layanan_" + 
                new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".doc\"");
            
            PrintWriter writer = response.getWriter();
            
            // Write HTML header for Word document
            writer.println("<html xmlns:o='urn:schemas-microsoft-com:office:office'");
            writer.println("xmlns:w='urn:schemas-microsoft-com:office:word'");
            writer.println("xmlns='http://www.w3.org/TR/REC-html40'>");
            writer.println("<head>");
            writer.println("<meta charset='UTF-8'>");
            writer.println("<meta name='ProgId' content='Word.Document'>");
            writer.println("<meta name='Generator' content='Microsoft Word'>");
            writer.println("<meta name='Originator' content='Microsoft Word'>");
            writer.println("<title>Data Layanan - Arqeta</title>");
            writer.println("<!--[if gte mso 9]>");
            writer.println("<xml>");
            writer.println("<w:WordDocument>");
            writer.println("<w:View>Print</w:View>");
            writer.println("<w:Zoom>90</w:Zoom>");
            writer.println("<w:DoNotPromptForConvert/>");
            writer.println("<w:DoNotShowRevisions/>");
            writer.println("<w:DoNotPrintRevisions/>");
            writer.println("<w:DoNotShowComments/>");
            writer.println("<w:DoNotShowInsertionsAndDeletions/>");
            writer.println("<w:DoNotShowPropertyChanges/>");
            writer.println("</w:WordDocument>");
            writer.println("</xml>");
            writer.println("<![endif]-->");
            writer.println("<style>");
            writer.println("body { font-family: 'Times New Roman', serif; margin: 1in; line-height: 1.5; }");
            writer.println("h1 { color: #2c3e50; text-align: center; font-size: 24pt; margin-bottom: 20pt; border-bottom: 3pt solid #3498db; padding-bottom: 10pt; }");
            writer.println(".company-info { text-align: center; margin-bottom: 30pt; }");
            writer.println(".company-name { font-size: 18pt; font-weight: bold; color: #2c3e50; margin-bottom: 5pt; }");
            writer.println(".export-info { text-align: center; margin-bottom: 20pt; color: #7f8c8d; font-size: 11pt; }");
            writer.println("table { width: 100%; border-collapse: collapse; margin-top: 20pt; font-size: 11pt; }");
            writer.println("th { border: 2pt solid #34495e; padding: 12pt 8pt; text-align: center; background-color: #3498db; color: white; font-weight: bold; font-size: 12pt; }");
            writer.println("td { border: 1pt solid #bdc3c7; padding: 10pt 8pt; text-align: left; vertical-align: top; }");
            writer.println("tr:nth-child(even) { background-color: #ecf0f1; }");
            writer.println("tr:hover { background-color: #d5dbdb; }");
            writer.println(".currency { text-align: right; font-weight: bold; color: #27ae60; }");
            writer.println(".number { text-align: center; font-weight: bold; }");
            writer.println(".image-cell { text-align: center; width: 120pt; }");
            writer.println(".service-image { max-width: 100pt; max-height: 80pt; border: 1pt solid #bdc3c7; border-radius: 4pt; }");
            writer.println(".no-image { color: #95a5a6; font-style: italic; font-size: 10pt; }");
            writer.println(".service-name { font-weight: bold; color: #2c3e50; }");
            writer.println(".date-cell { font-size: 10pt; color: #7f8c8d; }");
            writer.println(".summary-section { margin-bottom: 30pt; padding: 15pt; background-color: #ecf0f1; border-radius: 8pt; }");
            writer.println(".summary-title { font-size: 14pt; font-weight: bold; color: #2c3e50; margin-bottom: 10pt; }");
            writer.println(".summary-item { margin-bottom: 5pt; }");
            writer.println(".summary-label { font-weight: bold; color: #34495e; }");
            writer.println(".summary-value { color: #27ae60; font-weight: bold; }");
            writer.println("</style>");
            writer.println("</head>");
            writer.println("<body>");

            // Company header
            writer.println("<div class='company-info'>");
            writer.println("<div class='company-name'>PT. ARQETA DIGITAL SOLUTIONS</div>");
            writer.println("</div>");

            writer.println("<h1>LAPORAN DATA LAYANAN</h1>");
            writer.println("<div class='export-info'>Diekspor pada: " +
                new SimpleDateFormat("dd MMMM yyyy, HH:mm:ss").format(new Date()) + " WIB</div>");

            // Calculate summary statistics first
            String countQuery = "SELECT COUNT(*) as total_services, SUM(quantity) as total_quantity, AVG(price) as avg_price, MIN(price) as min_price, MAX(price) as max_price FROM services";
            PreparedStatement countPs = conn.prepareStatement(countQuery);
            ResultSet countRs = countPs.executeQuery();

            int totalServices = 0;
            int totalQuantity = 0;
            double avgPrice = 0;
            double minPrice = 0;
            double maxPrice = 0;

            if (countRs.next()) {
                totalServices = countRs.getInt("total_services");
                totalQuantity = countRs.getInt("total_quantity");
                avgPrice = countRs.getDouble("avg_price");
                minPrice = countRs.getDouble("min_price");
                maxPrice = countRs.getDouble("max_price");
            }
            countRs.close();
            countPs.close();

            // Summary section
            writer.println("<div class='summary-section'>");
            writer.println("<div class='summary-title'>Ringkasan Data Layanan</div>");
            writer.println("<div class='summary-item'><span class='summary-label'>Total Layanan:</span> <span class='summary-value'>" + totalServices + " layanan</span></div>");
            writer.println("<div class='summary-item'><span class='summary-label'>Total Kuantitas:</span> <span class='summary-value'>" + totalQuantity + " unit</span></div>");
            writer.println("<div class='summary-item'><span class='summary-label'>Harga Rata-rata:</span> <span class='summary-value'>Rp " + String.format("%,.0f", avgPrice) + "</span></div>");
            writer.println("<div class='summary-item'><span class='summary-label'>Harga Terendah:</span> <span class='summary-value'>Rp " + String.format("%,.0f", minPrice) + "</span></div>");
            writer.println("<div class='summary-item'><span class='summary-label'>Harga Tertinggi:</span> <span class='summary-value'>Rp " + String.format("%,.0f", maxPrice) + "</span></div>");
            writer.println("</div>");
            writer.println("<table>");
            writer.println("<thead>");
            writer.println("<tr>");
            writer.println("<th style='width: 8%;'>ID</th>");
            writer.println("<th style='width: 25%;'>NAMA LAYANAN</th>");
            writer.println("<th style='width: 20%;'>GAMBAR</th>");
            writer.println("<th style='width: 10%;'>JUMLAH</th>");
            writer.println("<th style='width: 15%;'>HARGA</th>");
            writer.println("<th style='width: 11%;'>DIBUAT</th>");
            writer.println("<th style='width: 11%;'>DIPERBARUI</th>");
            writer.println("</tr>");
            writer.println("</thead>");
            writer.println("<tbody>");
            
            // Write data rows
            boolean hasData = false;
            while (rs.next()) {
                hasData = true;
                int id = rs.getInt("id");
                String name = rs.getString("name");
                String images = rs.getString("images");
                int quantity = rs.getInt("quantity");
                double price = rs.getDouble("price");
                String createdAt = rs.getString("created_at");
                String updatedAt = rs.getString("updated_at");

                writer.println("<tr>");
                writer.println("<td class='number'><strong>" + id + "</strong></td>");
                writer.println("<td class='service-name'>" + (name != null ? name : "<em>Tidak ada nama</em>") + "</td>");

                // Handle image display with proper formatting
                writer.println("<td class='image-cell'>");
                if (images != null && !images.isEmpty()) {
                    // Split multiple images if separated by comma
                    String[] imageArray = images.split(",");
                    boolean hasValidImage = false;

                    for (int i = 0; i < Math.min(imageArray.length, 2); i++) { // Show max 2 images
                        String imagePath = imageArray[i].trim();
                        if (!imagePath.isEmpty()) {
                            // Convert image to base64 for embedding in Word document
                            String base64Image = getImageAsBase64(imagePath, application);
                            if (base64Image != null) {
                                writer.println("<img src='" + base64Image + "' class='service-image' alt='Gambar Layanan " + (i+1) + "' style='max-width: 100pt; max-height: 80pt; border: 1pt solid #bdc3c7; border-radius: 4pt; margin: 2pt;'/>");
                                hasValidImage = true;
                                if (i < imageArray.length - 1 && i < 1) {
                                    writer.println("<br/>");
                                }
                            } else {
                                // Fallback: show image name if base64 conversion fails
                                writer.println("<div class='image-name' style='font-size: 9pt; color: #7f8c8d; margin: 2pt;'>" + imagePath + "</div>");
                            }
                        }
                    }

                    if (!hasValidImage && imageArray.length > 0) {
                        writer.println("<div class='no-image'>Gambar: " + String.join(", ", imageArray) + "</div>");
                    }

                    if (imageArray.length > 2) {
                        writer.println("<div class='no-image'>+" + (imageArray.length - 2) + " gambar lainnya</div>");
                    }
                } else {
                    writer.println("<div class='no-image'>Tidak ada gambar</div>");
                }
                writer.println("</td>");

                writer.println("<td class='number'><strong>" + quantity + "</strong></td>");
                writer.println("<td class='currency'>Rp " + String.format("%,.0f", price) + "</td>");
                writer.println("<td class='date-cell'>" + (createdAt != null ? createdAt.substring(0, Math.min(createdAt.length(), 16)) : "-") + "</td>");
                writer.println("<td class='date-cell'>" + (updatedAt != null ? updatedAt.substring(0, Math.min(updatedAt.length(), 16)) : "-") + "</td>");
                writer.println("</tr>");
            }

            // If no data found
            if (!hasData) {
                writer.println("<tr>");
                writer.println("<td colspan='7' style='text-align: center; padding: 30pt; color: #7f8c8d; font-style: italic;'>Tidak ada data layanan yang ditemukan</td>");
                writer.println("</tr>");
            }
            
            writer.println("</tbody>");
            writer.println("</table>");

            // Footer section
            writer.println("<div style='margin-top: 40pt; text-align: center; font-size: 10pt; color: #7f8c8d; border-top: 2pt solid #bdc3c7; padding-top: 20pt;'>");
            writer.println("<p><strong>Laporan ini dibuat secara otomatis oleh sistem Arqeta</strong></p>");
            writer.println("<p>© " + new SimpleDateFormat("yyyy").format(new Date()) + " PT. Arqeta Digital Solutions. Semua hak dilindungi.</p>");
            writer.println("<p><em>Dokumen ini dapat dibuka dan diedit menggunakan Microsoft Word</em></p>");
            writer.println("</div>");

            writer.println("</body>");
            writer.println("</html>");
            
            writer.flush();
            writer.close();
            
        } else if (format.equals("json")) {
            // Export to JSON
            response.setContentType("application/json");
            response.setHeader("Content-Disposition", "attachment; filename=\"data_layanan_" +
                new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".json\"");

            PrintWriter writer = response.getWriter();

            // First, collect all data in memory to get accurate count
            ArrayList<Map<String, Object>> servicesList = new ArrayList<>();

            while (rs.next()) {
                Map<String, Object> service = new HashMap<>();
                service.put("id", rs.getInt("id"));
                service.put("name", rs.getString("name"));
                service.put("images", rs.getString("images"));
                service.put("quantity", rs.getInt("quantity"));
                service.put("price", rs.getDouble("price"));
                service.put("created_at", rs.getString("created_at"));
                service.put("updated_at", rs.getString("updated_at"));
                servicesList.add(service);
            }

            // Build JSON structure
            writer.println("{");
            writer.println("  \"export_info\": {");
            writer.println("    \"title\": \"Data Layanan - Arqeta\",");
            writer.println("    \"exported_at\": \"" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "\",");
            writer.println("    \"format\": \"JSON\",");
            writer.println("    \"total_records\": " + servicesList.size());
            writer.println("  },");
            writer.println("  \"services\": [");

            // Write data
            for (int i = 0; i < servicesList.size(); i++) {
                Map<String, Object> service = servicesList.get(i);

                if (i > 0) {
                    writer.println(",");
                }

                String name = (String) service.get("name");
                String images = (String) service.get("images");
                String createdAt = (String) service.get("created_at");
                String updatedAt = (String) service.get("updated_at");

                writer.println("    {");
                writer.println("      \"id\": " + service.get("id") + ",");
                writer.println("      \"name\": \"" + (name != null ? name.replace("\"", "\\\"") : "Tidak ada nama") + "\",");
                writer.println("      \"images\": \"" + (images != null ? images.replace("\"", "\\\"") : "") + "\",");
                writer.println("      \"quantity\": " + service.get("quantity") + ",");
                writer.println("      \"price\": " + service.get("price") + ",");
                writer.println("      \"price_formatted\": \"Rp " + String.format("%,.0f", (Double) service.get("price")) + "\",");
                writer.println("      \"created_at\": \"" + (createdAt != null ? createdAt : "") + "\",");
                writer.print("      \"updated_at\": \"" + (updatedAt != null ? updatedAt : "") + "\"");
                writer.print("    }");
            }

            writer.println();
            writer.println("  ]");
            writer.println("}");

            writer.flush();
            writer.close();
        }
        
        rs.close();
        ps.close();
        
    } catch (SQLException e) {
        session.setAttribute("notification", "Error saat mengekspor data: " + e.getMessage());
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=services");
    } catch (Exception e) {
        session.setAttribute("notification", "Error sistem: " + e.getMessage());
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=services");
    }
%>
